// TypeScript Deep Dive - Main Entry Point
// This file will help you navigate through your learning journey

console.log("🚀 Welcome to TypeScript Deep Dive!");
console.log("📚 Let's start with Day 1: TypeScript Fundamentals");
console.log("");

// Import and run the current lesson
// Uncomment the lesson you want to practice:

// Day 1 Morning Lessons:
// import "./day1/morning/01-basic-types";
// import "./day1/morning/explicit-types-example";  // Understanding explicit types
// import "./day1/morning/02-type-annotations";
import "./day1/morning/03-interfaces";
// import "./day1/morning/04-functions";
// import "./day1/morning/05-union-intersection";

// Day 1 Afternoon Lessons:
// import "./day1/afternoon/06-classes";
// import "./day1/afternoon/07-enums";
// import "./day1/afternoon/08-null-handling";
// import "./day1/afternoon/09-type-guards";

// Practice Projects:
// import "./day1/practice/data-structures";

console.log("✅ Lesson completed! Check the console output above.");
console.log("💡 Tip: Uncomment the next lesson in src/index.ts to continue learning.");
