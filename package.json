{"name": "complete_typescript", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "nodemon --exec ts-node src/index.ts", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^24.0.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}